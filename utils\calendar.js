/**
 * 农历和万年历工具类
 */
class Calendar {
  constructor() {
    // 农历数据
    this.lunarInfo = [
      0x04bd8, 0x04ae0, 0x0a570, 0x054d5, 0x0d260, 0x0d950, 0x16554, 0x056a0, 0x09ad0, 0x055d2,
      0x04ae0, 0x0a5b6, 0x0a4d0, 0x0d250, 0x1d255, 0x0b540, 0x0d6a0, 0x0ada2, 0x095b0, 0x14977,
      0x04970, 0x0a4b0, 0x0b4b5, 0x06a50, 0x06d40, 0x1ab54, 0x02b60, 0x09570, 0x052f2, 0x04970,
      0x06566, 0x0d4a0, 0x0ea50, 0x06e95, 0x05ad0, 0x02b60, 0x186e3, 0x092e0, 0x1c8d7, 0x0c950,
      0x0d4a0, 0x1d8a6, 0x0b550, 0x056a0, 0x1a5b4, 0x025d0, 0x092d0, 0x0d2b2, 0x0a950, 0x0b557
    ]

    // 生肖
    this.animals = ["鼠", "牛", "虎", "兔", "龙", "蛇", "马", "羊", "猴", "鸡", "狗", "猪"]
    
    // 天干
    this.gan = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"]
    
    // 地支
    this.zhi = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"]
    
    // 月份
    this.months = ["正", "二", "三", "四", "五", "六", "七", "八", "九", "十", "冬", "腊"]
    
    // 日期
    this.days = ["初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十",
      "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十",
      "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十"]

    // 节气
    this.solarTerms = {
      "立春": "2月3-5日", "雨水": "2月18-20日",
      "惊蛰": "3月5-7日", "春分": "3月20-22日",
      "清明": "4月4-6日", "谷雨": "4月19-21日",
      "立夏": "5月5-7日", "小满": "5月20-22日",
      "芒种": "6月5-7日", "夏至": "6月21-22日",
      "小暑": "7月6-8日", "大暑": "7月22-24日",
      "立秋": "8月7-9日", "处暑": "8月22-24日",
      "白露": "9月7-9日", "秋分": "9月22-24日",
      "寒露": "10月8-10日", "霜降": "10月23-24日",
      "立冬": "11月7-8日", "小雪": "11月22-23日",
      "大雪": "12月6-8日", "冬至": "12月21-23日"
    }
  }

  /**
   * 获取当前日期的农历信息
   * @returns {Object} 农历信息
   */
  getTodayLunar() {
    const date = new Date()
    return this.solar2lunar(date.getFullYear(), date.getMonth() + 1, date.getDate())
  }

  /**
   * 获取当前节气信息
   * @returns {Object} 节气信息
   */
  getCurrentSolarTerm() {
    const date = new Date()
    const month = date.getMonth() + 1
    const day = date.getDate()
    
    let currentTerm = null
    for (let term in this.solarTerms) {
      const range = this.solarTerms[term]
      const [startMonth, dayRange] = range.split('月')
      const [startDay, endDay] = dayRange.split('-').map(d => parseInt(d))
      
      if (parseInt(startMonth) === month && day >= startDay && day <= endDay) {
        currentTerm = {
          name: term,
          range: range
        }
        break
      }
    }
    
    return currentTerm
  }

  /**
   * 公历转农历
   * @param {number} year 年
   * @param {number} month 月
   * @param {number} day 日
   * @returns {Object} 农历日期信息
   */
  solar2lunar(year, month, day) {
    // 这里实现公历转农历的具体算法
    // 由于农历算法比较复杂，这里只返回示例数据
    const lunarYear = this.gan[year % 10] + this.zhi[year % 12]
    const animal = this.animals[year % 12]
    
    return {
      year: year,
      month: month,
      day: day,
      lunarYear: lunarYear,
      lunarMonth: this.months[month - 1] + "月",
      lunarDay: this.days[day - 1],
      animal: animal,
      term: this.getCurrentSolarTerm()
    }
  }

  /**
   * 获取宜忌
   * @returns {Object} 宜忌信息
   */
  getTodayYiJi() {
    // 这里可以根据日期和节气等信息计算宜忌
    // 这里返回示例数据
    return {
      yi: ["祭祀", "开光", "出行", "理发"],
      ji: ["动土", "安葬", "开张"]
    }
  }

  /**
   * 获取今日完整信息
   * @returns {Object} 今日信息
   */
  getTodayInfo() {
    const date = new Date()
    const weekdays = ["日", "一", "二", "三", "四", "五", "六"]
    
    const solar = {
      year: date.getFullYear(),
      month: date.getMonth() + 1,
      day: date.getDate(),
      weekday: "星期" + weekdays[date.getDay()]
    }
    
    const lunar = this.solar2lunar(solar.year, solar.month, solar.day)
    const yiji = this.getTodayYiJi()
    
    return {
      solar,
      lunar,
      yiji
    }
  }
}

module.exports = new Calendar() 